/**
 * API Route: Chat with AI Agent
 * This implements the conversational interface for API agents
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import { CustomUser } from "@/lib/types";

import prisma from "../../../../lib/prisma";
import {
  APICallPlan,
  ConversationContext,
  ExecutionResult,
  FunctionTool,
  OpenAPISpec,
  createRestGPTEngine,
} from "../../../../lib/restgpt-engine";
import { authOptions } from "../../auth/[...nextauth]";

interface ChatRequest {
  message: string;
  sessionId?: string;
  context?: Record<string, any>;
}

/**
 * Filter OpenAPI spec to only include operations that match the given tool names
 */
function filterOpenAPISpecByToolNames(
  spec: OpenAPISpec,
  enabledToolNames: string[],
): OpenAPISpec {
  const filteredPaths: Record<string, any> = {};

  // Helper function to generate tool name from operation (same logic as in parser)
  function generateToolName(
    path: string,
    method: string,
    operationId?: string,
  ): string {
    if (operationId) {
      // Convert camelCase to snake_case
      return operationId
        .replace(/([A-Z])/g, "_$1")
        .toLowerCase()
        .replace(/^_/, "");
    }

    const pathParts = path
      .split("/")
      .filter((part) => part && !part.startsWith("{"));
    const methodLower = method.toLowerCase();

    if (pathParts.length > 0) {
      const resource = pathParts[pathParts.length - 1];
      return `${methodLower}_${resource}`;
    }

    return `${methodLower}_endpoint`;
  }

  // Iterate through all paths and operations
  for (const [path, pathItem] of Object.entries(spec.paths)) {
    const filteredPathItem: Record<string, any> = {};
    let hasEnabledOperations = false;

    for (const [method, operation] of Object.entries(pathItem)) {
      if (typeof operation === "object" && operation !== null) {
        const op = operation as any;
        const toolName = generateToolName(path, method, op.operationId);

        // Only include this operation if its tool name is in the enabled list
        if (enabledToolNames.includes(toolName)) {
          filteredPathItem[method] = operation;
          hasEnabledOperations = true;
        }
      }
    }

    // Only include the path if it has at least one enabled operation
    if (hasEnabledOperations) {
      filteredPaths[path] = filteredPathItem;
    }
  }

  // Return filtered spec with same structure but only enabled operations
  return {
    ...spec,
    paths: filteredPaths,
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { agentId } = req.query;
  if (!agentId || typeof agentId !== "string") {
    return res.status(400).json({ error: "Invalid agent ID" });
  }

  // Handle initial data fetch for the chat UI
  if (req.method === "GET") {
    // Get agent from database
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        OR: [
          { ownerId: (session.user as any).id },
          { team: { users: { some: { userId: (session.user as any).id } } } },
        ],
      },
      include: {
        apiSpec: {
          select: {
            id: true,
            name: true,
          },
        },
        tools: {
          select: {
            id: true,
          },
        },
      },
    });

    if (!agent) {
      return res.status(404).json({
        error: "Agent not found",
      });
    }

    const formattedAgent = {
      id: agent.id,
      name: agent.name,
      description: agent.description,
      specId: agent.apiSpecId,
      specName: agent.apiSpec.name,
      status: agent.status.toLowerCase(),
      toolCount: agent.tools.length,
      conversationCount: agent.conversationCount,
      configuration: agent.modelConfig,
    };

    const emptyMessages: any[] = [];

    const conversationStub = {
      id: `conv_${Date.now()}`,
      title: "New Conversation",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    return res.status(200).json({
      success: true,
      data: {
        agent: formattedAgent,
        messages: emptyMessages,
        conversation: conversationStub,
      },
    });
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { message, sessionId, context }: ChatRequest = req.body;

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Get agent configuration from database
    const agent = await prisma.agent.findFirst({
      where: {
        id: agentId,
        OR: [
          { ownerId: (session.user as CustomUser).id },
          {
            team: {
              users: { some: { userId: (session.user as CustomUser).id } },
            },
          },
        ],
      },
      include: {
        apiSpec: true,
        tools: {
          include: {
            tool: true,
          },
          where: {
            enabled: true,
          },
        },
      },
    });

    if (!agent) {
      return res.status(404).json({
        error: "Agent not found",
      });
    }

    if (!agent.apiSpec.openApiSpec) {
      return res.status(500).json({
        error: "Agent API spec is missing or invalid",
      });
    }

    // Get the list of enabled tool names for this agent
    const enabledToolNames = agent.tools
      .filter((at) => at.enabled)
      .map((at) => at.tool.name);

    console.log(
      `Agent has ${enabledToolNames.length} enabled tools:`,
      enabledToolNames,
    );

    // Create a filtered OpenAPI spec that only includes operations for enabled tools
    const fullSpec = agent.apiSpec.openApiSpec as unknown as OpenAPISpec;
    const filteredSpec = filterOpenAPISpecByToolNames(
      fullSpec,
      enabledToolNames,
    );

    console.log(
      `Filtered OpenAPI spec to ${Object.keys(filteredSpec.paths).length} paths`,
    );

    // Initialize RestGPT engine with filtered spec
    const engine = createRestGPTEngine({
      enableLogging: true,
    });

    const initResult = await engine.initialize(
      filteredSpec,
      agent.apiSpec.baseUrl,
    );

    if (!initResult.success) {
      return res.status(500).json({
        error: "Failed to initialize agent engine",
      });
    }

    // Build conversation context including the agent's system prompt
    const conversationContext: ConversationContext = {
      sessionId: sessionId || `session_${Date.now()}`,
      userId:
        (session.user as { id?: string | null }).id ??
        session.user.email ??
        "anonymous",
      agentId,
      messageHistory: [
        {
          role: "system",
          content: agent.systemPrompt || "You are a helpful AI assistant.",
          timestamp: new Date(),
        },
        {
          role: "user",
          content: message,
          timestamp: new Date(),
        },
      ],
      executionHistory: [],
    };

    // Process the user request
    console.log("Processing user request:", message);
    const result = await engine.processRequest(message, conversationContext);
    console.log("Engine result:", {
      success: result.success,
      response: result.response,
      executionHistoryLength: result.executionHistory.length,
      error: result.error,
    });

    if (!result.success) {
      return res.status(500).json({
        error: "Failed to process request",
        message: result.error,
      });
    }

    // Save conversation to database
    let conversation = await prisma.agentConversation.findUnique({
      where: { sessionId: conversationContext.sessionId },
    });

    if (!conversation) {
      conversation = await prisma.agentConversation.create({
        data: {
          sessionId: conversationContext.sessionId,
          title: message.substring(0, 50) + (message.length > 50 ? "..." : ""),
          status: "ACTIVE",
          userId: (session.user as CustomUser).id,
          agentId,
        },
      });
    }

    // Save user message
    await prisma.agentMessage.create({
      data: {
        content: message,
        role: "USER",
        conversationId: conversation.id,
      },
    });

    // Save assistant response
    await prisma.agentMessage.create({
      data: {
        content: result.response,
        role: "ASSISTANT",
        conversationId: conversation.id,
        apiExecutions: result.executionHistory.map(
          (exec: { plan: APICallPlan; result: ExecutionResult }) => ({
            tool: exec.plan.toolId,
            success: exec.result.success,
            responseTime: exec.result.responseTime,
          }),
        ),
      },
    });

    // Update conversation stats
    await prisma.agentConversation.update({
      where: { id: conversation.id },
      data: {
        messageCount: { increment: 2 }, // user + assistant
        apiCallCount: { increment: result.executionHistory.length },
        lastMessageAt: new Date(),
      },
    });

    // Update agent stats
    await prisma.agent.update({
      where: { id: agentId },
      data: {
        messageCount: { increment: 2 },
        lastUsed: new Date(),
      },
    });

    // Set up Server-Sent Events headers
    res.writeHead(200, {
      "Content-Type": "text/plain; charset=utf-8",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    });

    // Send the response as streaming content
    const responseText = result.response;

    // Stream the response character by character for a typing effect
    for (let i = 0; i < responseText.length; i++) {
      const chunk = responseText[i];
      res.write(
        `data: ${JSON.stringify({ type: "content", content: chunk })}\n\n`,
      );

      // Add a small delay for typing effect (optional)
      await new Promise((resolve) => setTimeout(resolve, 20));
    }

    // Send completion signal
    res.write(`data: ${JSON.stringify({ type: "done" })}\n\n`);
    res.end();
  } catch (error) {
    console.error("Chat error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
