/**
 * Main RestGPT Engine - Orchestrates Parser, Planner, and Executor
 * Implements the full Parse -> Plan -> Execute workflow
 */
import { Agent, run } from "@openai/agents";

import { APIExecutor } from "./executor";
import { APISpecParser } from "./parser";
import { TaskPlanner } from "./planner";
import {
  APICallPlan,
  ConversationContext,
  EngineConfig,
  ExecutionResult,
  FunctionTool,
  NaturalLanguagePlan,
  OpenAPISpec,
} from "./types";

export class RestGPTEngine {
  private parser: APISpecParser;
  private planner: TaskPlanner;
  private executor: APIExecutor;
  private conversationalAgent: Agent;
  private tools: FunctionTool[] = [];
  private baseUrl: string = "";

  constructor(private config: EngineConfig) {
    this.parser = new APISpecParser();
    this.planner = new TaskPlanner(config);
    this.executor = new APIExecutor(config);

    // Initialize conversational agent for non-API requests
    this.conversationalAgent = new Agent({
      name: "conversational-agent",
      model: "gpt-4o-mini",
      instructions: `You are a helpful AI assistant that can engage in natural conversation.
      You can answer questions, provide explanations, and have friendly conversations.
      When users greet you or ask general questions that don't require API operations,
      respond naturally and helpfully. Keep your responses concise but informative.`,
    });
  }

  /**
   * Initialize the engine with an OpenAPI specification
   */
  async initialize(
    spec: OpenAPISpec,
    baseUrl: string,
  ): Promise<{
    success: boolean;
    tools: FunctionTool[];
    errors?: string[];
  }> {
    try {
      // Validate the OpenAPI spec
      const validation = this.parser.validateOpenAPISpec(spec);
      if (!validation.valid) {
        return {
          success: false,
          tools: [],
          errors: validation.errors,
        };
      }

      this.baseUrl = baseUrl;

      // Parse the spec and generate tools
      const endpoints = this.parser.parseOpenAPISpec(spec);
      this.tools = this.parser.generateFunctionTools(endpoints, baseUrl);

      // Initialize agents
      await this.planner.initialize(this.tools);
      await this.executor.initialize(this.tools);

      if (this.config.enableLogging) {
        console.log(
          `RestGPT Engine initialized with ${this.tools.length} tools`,
        );
      }

      return {
        success: true,
        tools: this.tools,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown initialization error";
      return {
        success: false,
        tools: [],
        errors: [errorMessage],
      };
    }
  }

  /**
   * Process a user request using the Parse -> Plan -> Execute workflow
   */
  async processRequest(
    userRequest: string,
    context: ConversationContext,
  ): Promise<{
    success: boolean;
    response: string;
    executionHistory: Array<{
      plan: APICallPlan;
      result: ExecutionResult;
    }>;
    error?: string;
  }> {
    try {
      const executionHistory: Array<{
        plan: APICallPlan;
        result: ExecutionResult;
      }> = [];

      // Step 1: Create initial plan
      console.log("Creating initial plan for request:", userRequest);
      let currentPlan = await this.planner.createPlan(userRequest, context);
      console.log("Initial plan created:", currentPlan);

      // Check if this is a conversational request
      if (currentPlan.requestType === "conversational") {
        console.log("Handling conversational request");
        const response = await this.handleConversationalRequest(
          userRequest,
          context,
        );
        return {
          success: true,
          response,
          executionHistory: [],
        };
      }

      // Step 2: Execute plan iteratively for API requests
      while (currentPlan.nextAction !== "end") {
        // Convert natural language plan to API call plan
        const apiPlan = await this.createAPICallPlan(currentPlan, context);

        if (apiPlan) {
          // Execute the API call
          const result = await this.executor.executeAPICall(
            apiPlan,
            this.baseUrl,
            currentPlan.context,
          );

          executionHistory.push({ plan: apiPlan, result });

          // Update context with execution result
          context.executionHistory.push({
            plan: apiPlan,
            result,
            timestamp: new Date(),
          });

          // Check for specific errors that should end execution immediately
          if (!result.success && result.error) {
            if (
              result.error.includes("Body has already been read") ||
              result.error.includes("Body is unusable")
            ) {
              console.log("Detected body reuse error, ending execution");
              break;
            }
          }

          // Check for too many consecutive failures
          const recentFailures = context.executionHistory
            .slice(-3)
            .filter((exec) => !exec.result.success).length;

          if (recentFailures >= 3) {
            console.log("Too many consecutive failures, ending execution");
            break;
          }

          // Update plan based on result
          currentPlan = await this.planner.updatePlan(
            currentPlan,
            result,
            context,
          );
        } else {
          // No valid API plan could be created, end execution
          break;
        }

        // Safety check to prevent infinite loops
        if (executionHistory.length > 10) {
          break;
        }
      }

      // Generate final response
      const response = await this.generateFinalResponse(
        userRequest,
        executionHistory,
        currentPlan,
      );

      return {
        success: true,
        response,
        executionHistory,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown processing error";

      if (this.config.enableLogging) {
        console.error("RestGPT Engine processing error:", error);
      }

      return {
        success: false,
        response:
          "I encountered an error while processing your request. Please try again.",
        executionHistory: [],
        error: errorMessage,
      };
    }
  }

  /**
   * Handle conversational requests that don't require API calls
   */
  private async handleConversationalRequest(
    userRequest: string,
    context: ConversationContext,
  ): Promise<string> {
    try {
      // Build conversation context for the agent
      const conversationHistory = context.messageHistory
        .slice(-5) // Last 5 messages for context
        .map((msg) => `${msg.role}: ${msg.content}`)
        .join("\n");

      const prompt = `${conversationHistory}\nuser: ${userRequest}`;

      const result = await run(this.conversationalAgent, prompt);

      if (result.finalOutput) {
        return result.finalOutput;
      }

      // Fallback response
      return "Hello! I'm here to help you with API operations and general questions. How can I assist you today?";
    } catch (error) {
      console.error("Conversational request error:", error);
      return "Hello! I'm here to help you with API operations and general questions. How can I assist you today?";
    }
  }

  /**
   * Get available tools (for frontend display)
   */
  getAvailableTools(): FunctionTool[] {
    return this.tools.filter((tool) => tool.enabled);
  }

  /**
   * Update tool configuration
   */
  updateTool(toolId: string, updates: Partial<FunctionTool>): boolean {
    const toolIndex = this.tools.findIndex((tool) => tool.id === toolId);
    if (toolIndex === -1) return false;

    this.tools[toolIndex] = {
      ...this.tools[toolIndex],
      ...updates,
      userModified: true,
    };

    return true;
  }

  /**
   * Enable/disable a tool
   */
  toggleTool(toolId: string, enabled: boolean): boolean {
    return this.updateTool(toolId, { enabled });
  }

  /**
   * Create API call plan from natural language plan
   */
  private async createAPICallPlan(
    nlPlan: NaturalLanguagePlan,
    context: ConversationContext,
  ): Promise<APICallPlan | null> {
    // This is a simplified version - in a full implementation,
    // this would use an API Selector agent to choose the right tool

    const availableTools = this.tools.filter((tool) => tool.enabled);
    if (availableTools.length === 0) return null;

    // For now, use the first available tool
    // In practice, this would be more sophisticated tool selection
    const selectedTool = availableTools[0];

    return {
      toolId: selectedTool.id,
      endpoint: selectedTool.endpoint,
      parameters: nlPlan.context,
      expectedResponse: nlPlan.currentTask,
      outputInstruction: this.parser.generateParsingInstructions(
        selectedTool.endpoint,
        nlPlan.currentTask,
      ),
    };
  }

  /**
   * Generate final response based on execution history
   */
  private async generateFinalResponse(
    userRequest: string,
    executionHistory: Array<{ plan: APICallPlan; result: ExecutionResult }>,
    finalPlan: NaturalLanguagePlan,
  ): Promise<string> {
    if (executionHistory.length === 0) {
      // Check if this was supposed to be an API request but no tools were available
      if (finalPlan.requestType === "api") {
        return "I understand you want to perform an API operation, but I couldn't find any suitable API tools to complete your request. Please make sure the API specification is properly configured and the required tools are enabled.";
      }
      return "I was unable to find any suitable actions to complete your request.";
    }

    const successfulExecutions = executionHistory.filter(
      (exec) => exec.result.success,
    );
    const failedExecutions = executionHistory.filter(
      (exec) => !exec.result.success,
    );

    let response = `I've processed your request: "${userRequest}"\n\n`;

    if (successfulExecutions.length > 0) {
      response += "✅ Successfully executed:\n";
      successfulExecutions.forEach((exec, index) => {
        response += `${index + 1}. ${exec.plan.expectedResponse}\n`;
        if (exec.result.parsedOutput) {
          response += `   📋 Result: ${JSON.stringify(exec.result.parsedOutput, null, 2)}\n`;
        } else if (exec.result.data) {
          response += `   📋 Data: ${JSON.stringify(exec.result.data, null, 2)}\n`;
        }
      });
    }

    if (failedExecutions.length > 0) {
      response += "\n❌ Some operations failed:\n";
      failedExecutions.forEach((exec, index) => {
        const errorMsg = exec.result.error || "Unknown error";
        response += `${index + 1}. ${exec.plan.expectedResponse}: ${errorMsg}\n`;

        // Provide helpful error explanations
        if (
          errorMsg.includes("Body has already been read") ||
          errorMsg.includes("Body is unusable")
        ) {
          response += `   💡 This appears to be a technical issue with request handling. Please try your request again.\n`;
        } else if (errorMsg.includes("404")) {
          response += `   💡 The requested resource was not found. Please check if the ID or parameters are correct.\n`;
        } else if (errorMsg.includes("401") || errorMsg.includes("403")) {
          response += `   💡 Authentication or authorization failed. Please check your API credentials.\n`;
        }
      });
    }

    // Add summary
    if (successfulExecutions.length > 0 && failedExecutions.length === 0) {
      response += "\n🎉 All operations completed successfully!";
    } else if (successfulExecutions.length > 0 && failedExecutions.length > 0) {
      response += `\n⚠️ Completed ${successfulExecutions.length} operations successfully, but ${failedExecutions.length} failed.`;
    } else {
      response +=
        "\n😞 Unfortunately, all operations failed. Please check the errors above and try again.";
    }

    return response;
  }

  /**
   * Health check for the engine
   */
  async healthCheck(): Promise<{
    status: "healthy" | "degraded" | "unhealthy";
    components: {
      parser: boolean;
      planner: boolean;
      executor: boolean;
    };
    toolCount: number;
  }> {
    const components = {
      parser: this.parser !== null,
      planner: this.planner !== null,
      executor: this.executor !== null,
    };

    const healthyComponents = Object.values(components).filter(Boolean).length;
    const status =
      healthyComponents === 3
        ? "healthy"
        : healthyComponents >= 2
          ? "degraded"
          : "unhealthy";

    return {
      status,
      components,
      toolCount: this.tools.length,
    };
  }
}

// Export all components for external use
export { APISpecParser } from "./parser";
export { TaskPlanner } from "./planner";
export { APIExecutor } from "./executor";
export * from "./types";
