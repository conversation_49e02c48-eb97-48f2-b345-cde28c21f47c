/**
 * API Route: Chat with AI Agent for API Specification
 * This implements the conversational interface for API specification agents
 */
import { NextApiRequest, NextApiResponse } from "next";

import { getServerSession } from "next-auth/next";

import { CustomUser } from "@/lib/types";

import prisma from "../../../../../../../lib/prisma";
import {
  ConversationContext,
  OpenAPISpec,
  createRestGPTEngine,
} from "../../../../../../../lib/restgpt-engine";
import { authOptions } from "../../../../../auth/[...nextauth]";

interface ChatRequest {
  message: string;
  functionTools?: any[];
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user) {
    return res.status(401).json({ error: "Unauthorized" });
  }

  const { teamId, specId } = req.query;
  if (!teamId || typeof teamId !== "string") {
    return res.status(400).json({ error: "Invalid team ID" });
  }
  if (!specId || typeof specId !== "string") {
    return res.status(400).json({ error: "Invalid spec ID" });
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { message, functionTools }: ChatRequest = req.body;

    if (!message) {
      return res.status(400).json({ error: "Message is required" });
    }

    // Get API specification from database
    const apiSpec = await prisma.apiSpecification.findFirst({
      where: {
        id: specId,
        teamId: teamId,
        OR: [
          { ownerId: (session.user as CustomUser).id },
          {
            team: {
              users: { some: { userId: (session.user as CustomUser).id } },
            },
          },
        ],
      },
    });

    if (!apiSpec) {
      return res.status(404).json({
        error: "API specification not found",
      });
    }

    if (!apiSpec.openApiSpec) {
      return res.status(500).json({
        error: "API specification is missing or invalid",
      });
    }

    // Initialize RestGPT engine with the API spec
    const engine = createRestGPTEngine({
      enableLogging: true,
    });

    const initResult = await engine.initialize(
      apiSpec.openApiSpec as unknown as OpenAPISpec,
      apiSpec.baseUrl,
    );

    if (!initResult.success) {
      return res.status(500).json({
        error: "Failed to initialize AI agent",
        details: initResult.errors,
      });
    }

    // Create conversation context
    const conversationContext: ConversationContext = {
      sessionId: `spec-${specId}-${Date.now()}`,
      messageHistory: [
        {
          role: "user",
          content: message,
          timestamp: new Date(),
        },
      ],
      executionHistory: [],
      metadata: {
        apiSpecId: specId,
        teamId: teamId,
        userId: (session.user as CustomUser).id,
      },
    };

    // Process the user request
    console.log("Processing user request:", message);
    const result = await engine.processRequest(message, conversationContext);
    console.log("Engine result:", {
      success: result.success,
      response: result.response,
      executionHistoryLength: result.executionHistory.length,
      error: result.error,
    });

    if (!result.success) {
      return res.status(500).json({
        error: "Failed to process request",
        message: result.error,
      });
    }

    // Return response in the format expected by the frontend
    res.status(200).json({
      success: true,
      message: result.response, // Frontend expects 'message' property
      executionDetails: result.executionHistory.map((exec) => ({
        tool: exec.plan.toolId,
        success: exec.result.success,
        responseTime: exec.result.responseTime,
      })),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Chat error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
